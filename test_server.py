#!/usr/bin/env python3
"""
测试服务器 - 不需要真实的Oracle数据库连接
"""

import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from unittest.mock import MagicMock

app = FastAPI(title="MCP Oracle Server - Test Mode")

# 模拟数据
MOCK_TABLES = [
    {"name": "EMPLOYEES", "type": "TABLE"},
    {"name": "DEPARTMENTS", "type": "TABLE"},
    {"name": "PROJECTS", "type": "TABLE"}
]

MOCK_COLUMNS = {
    "EMPLOYEES": [
        {"name": "ID", "type": "NUMBER", "length": 10},
        {"name": "NAME", "type": "VARCHAR2", "length": 50},
        {"name": "EMAIL", "type": "VARCHAR2", "length": 100},
        {"name": "DEPARTMENT_ID", "type": "NUMBER", "length": 10}
    ],
    "DEPARTMENTS": [
        {"name": "ID", "type": "NUMBER", "length": 10},
        {"name": "NAME", "type": "VARCHAR2", "length": 50},
        {"name": "MANAGER_ID", "type": "NUMBER", "length": 10}
    ]
}

MOCK_DATA = {
    "EMPLOYEES": [
        {"ID": 1, "NAME": "John Doe", "EMAIL": "<EMAIL>", "DEPARTMENT_ID": 1},
        {"ID": 2, "NAME": "Jane Smith", "EMAIL": "<EMAIL>", "DEPARTMENT_ID": 2},
        {"ID": 3, "NAME": "Bob Johnson", "EMAIL": "<EMAIL>", "DEPARTMENT_ID": 1}
    ],
    "DEPARTMENTS": [
        {"ID": 1, "NAME": "Engineering", "MANAGER_ID": 1},
        {"ID": 2, "NAME": "Marketing", "MANAGER_ID": 2}
    ]
}

class DDLRequest(BaseModel):
    sql: str

@app.get("/")
async def root():
    return {"message": "MCP Oracle Server - Test Mode", "status": "running"}

@app.get("/databases")
async def get_database_info():
    return {"name": "TEST_DATABASE"}

@app.get("/tables")
async def get_tables(schema: str = "USER"):
    return {"tables": MOCK_TABLES}

@app.get("/tables/{table_name}/columns")
async def get_table_columns(table_name: str):
    table_upper = table_name.upper()
    if table_upper in MOCK_COLUMNS:
        return {"columns": MOCK_COLUMNS[table_upper]}
    else:
        raise HTTPException(status_code=404, detail="Table not found")

@app.post("/tables/{table_name}/query")
async def query_table(table_name: str, limit: int = 10):
    table_upper = table_name.upper()
    if table_upper in MOCK_DATA:
        data = MOCK_DATA[table_upper][:limit]
        return {"data": data}
    else:
        raise HTTPException(status_code=404, detail="Table not found")

@app.post("/tables/{table_name}/insert")
async def insert_data(table_name: str, data: dict):
    table_upper = table_name.upper()
    if table_upper in MOCK_DATA:
        # 模拟插入成功
        return {"status": "success", "row_count": 1}
    else:
        raise HTTPException(status_code=404, detail="Table not found")

@app.post("/ddl")
async def execute_ddl(request: DDLRequest):
    sql = request.sql
    allowed_ddl = ["CREATE", "ALTER", "COMMENT"]
    if not any(sql.upper().startswith(k) for k in allowed_ddl):
        raise HTTPException(403, "Only DDL operations allowed")

    forbidden_keywords = ["DROP", "TRUNCATE"]
    if any(kw in sql.upper() for kw in forbidden_keywords):
        raise HTTPException(403, "Forbidden SQL operation detected")

    # 模拟DDL执行成功
    return {"status": "success", "sql_executed": sql}

if __name__ == "__main__":
    print("启动测试服务器...")
    print("访问 http://localhost:8000/docs 查看API文档")
    uvicorn.run(app, host="0.0.0.0", port=8000)
