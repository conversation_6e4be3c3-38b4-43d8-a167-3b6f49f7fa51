# Oracle数据库MCP服务端

## 环境准备
1. 安装依赖：
   ```bash
   pip install fastapi uvicorn cx_Oracle
   ```

2. 配置环境变量（替换为实际值）：
   ```bash
   export ORACLE_USER=your_username  
   export ORACLE_PASSWORD=your_password  
   export ORACLE_DSN=localhost/XE  # 格式：//host:port/service_name
   ```

## 启动服务
```bash
# 直接运行（无热重载）
python -m uvicorn mcp_oracle_server:app

# 开发模式（自动重启）
./run.sh  # Linux/MacOS用户
```

## 接口文档
访问 [http://localhost:8000/docs](http://localhost:8000/docs) 查看交互式API文档。

### 核心接口列表：
| 方法 | 路径 | 功能 |
|------|------|------|
| GET | /databases | 获取当前数据库名称 |
| GET | /tables?schema=SCHEMA_NAME | 列出指定模式下的表（默认USER） |
| POST | /ddl | 执行DDL操作（CREATE/ALTER等） |
| POST | /tables/{table}/query | 查询指定表数据（支持分页） |
| POST | /tables/{table}/insert | 插入新记录 |

### 安全注意事项：
1. 生产环境需配置认证机制（如JWT）
2. 禁止直接暴露接口到公网
3. DDL操作建议添加二次确认步骤

## 使用示例：
```python
import requests

# 查询表结构
resp = requests.get("http://localhost:8000/tables/employees/columns")
print(resp.json())

# 执行DDL创建新表
requests.post(
    "http://localhost:8000/ddl",
    json={
        "sql": "CREATE TABLE test_table (id NUMBER PRIMARY KEY, name VARCHAR2(50))"
    }
)
```

## 错误代码说明：
- 403：非法操作类型（如尝试执行DELETE语句）
- 404：表不存在
- 500：数据库连接失败或其他内部错误
