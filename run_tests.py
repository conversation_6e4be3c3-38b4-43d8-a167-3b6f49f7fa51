#!/usr/bin/env python3
"""
简单的测试运行器
"""

import sys
import traceback
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

# 导入我们的应用
try:
    from mcp_oracle_server import app
    print("✓ 成功导入 mcp_oracle_server")
except Exception as e:
    print(f"✗ 导入 mcp_oracle_server 失败: {e}")
    traceback.print_exc()
    sys.exit(1)

# 创建测试客户端
client = TestClient(app)

def test_basic_import():
    """测试基本导入"""
    print("测试 1: 基本导入")
    try:
        import oracledb
        import fastapi
        print("✓ 所有依赖导入成功")
        return True
    except Exception as e:
        print(f"✗ 依赖导入失败: {e}")
        return False

def test_app_creation():
    """测试应用创建"""
    print("测试 2: FastAPI 应用创建")
    try:
        assert app is not None
        print("✓ FastAPI 应用创建成功")
        return True
    except Exception as e:
        print(f"✗ FastAPI 应用创建失败: {e}")
        return False

def test_database_endpoint_mock():
    """测试数据库端点（模拟）"""
    print("测试 3: 数据库端点（模拟）")
    try:
        with patch("mcp_oracle_server.get_connection") as mock_conn:
            # 创建模拟的连接和游标
            conn = MagicMock()
            cursor = MagicMock()
            conn.cursor.return_value = cursor
            mock_conn.return_value = conn
            cursor.fetchone.return_value = ["TEST_DB"]
            
            response = client.get("/databases")
            
            assert response.status_code == 200
            assert response.json() == {"name": "TEST_DB"}
            print("✓ 数据库端点测试成功")
            return True
    except Exception as e:
        print(f"✗ 数据库端点测试失败: {e}")
        traceback.print_exc()
        return False

def test_tables_endpoint_mock():
    """测试表列表端点（模拟）"""
    print("测试 4: 表列表端点（模拟）")
    try:
        with patch("mcp_oracle_server.get_connection") as mock_conn:
            conn = MagicMock()
            cursor = MagicMock()
            conn.cursor.return_value = cursor
            mock_conn.return_value = conn
            cursor.__iter__.return_value = [("EMPLOYEES", "TABLE"), ("DEPARTMENTS", "TABLE")]
            
            response = client.get("/tables?schema=HR")
            
            assert response.status_code == 200
            assert "tables" in response.json()
            expected_tables = [{"name": "EMPLOYEES", "type": "TABLE"}, {"name": "DEPARTMENTS", "type": "TABLE"}]
            assert response.json()["tables"] == expected_tables
            print("✓ 表列表端点测试成功")
            return True
    except Exception as e:
        print(f"✗ 表列表端点测试失败: {e}")
        traceback.print_exc()
        return False

def test_ddl_endpoint_mock():
    """测试DDL端点（模拟）"""
    print("测试 5: DDL端点（模拟）")
    try:
        with patch("mcp_oracle_server.get_connection") as mock_conn:
            conn = MagicMock()
            cursor = MagicMock()
            conn.cursor.return_value = cursor
            mock_conn.return_value = conn
            
            sql = "CREATE TABLE test_table (id NUMBER PRIMARY KEY, name VARCHAR2(50))"
            response = client.post("/ddl", json={"sql": sql})
            
            assert response.status_code == 200
            assert response.json()["status"] == "success"
            assert response.json()["sql_executed"] == sql
            print("✓ DDL端点测试成功")
            return True
    except Exception as e:
        print(f"✗ DDL端点测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始运行 MCP Oracle 服务器测试...")
    print("=" * 50)
    
    tests = [
        test_basic_import,
        test_app_creation,
        test_database_endpoint_mock,
        test_tables_endpoint_mock,
        test_ddl_endpoint_mock
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            traceback.print_exc()
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
