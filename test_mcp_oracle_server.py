import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from mcp_oracle_server import app

client = TestClient(app)

# 模拟数据库连接
@pytest.fixture
def mock_connection():
    with patch("mcp_oracle_server.get_connection") as mock_conn:
        # 创建模拟的连接和游标
        conn = MagicMock()
        cursor = MagicMock()
        conn.cursor.return_value = cursor
        mock_conn.return_value = conn
        yield conn, cursor

def test_get_database_info(mock_connection):
    conn, cursor = mock_connection
    cursor.fetchone.return_value = ["TEST_DB"]

    response = client.get("/databases")

    assert response.status_code == 200
    assert response.json() == {"name": "TEST_DB"}
    cursor.execute.assert_called_with("SELECT name FROM v$database")

def test_get_tables(mock_connection):
    conn, cursor = mock_connection
    cursor.__iter__.return_value = [("EMPLOYEES", "TABLE"), ("DEPARTMENTS", "TABLE")]

    response = client.get("/tables?schema=HR")

    assert response.status_code == 200
    assert "tables" in response.json()
    expected_tables = [{"name": "EMPLOYEES", "type": "TABLE"}, {"name": "DEPARTMENTS", "type": "TABLE"}]
    assert response.json()["tables"] == expected_tables

def test_get_table_columns(mock_connection):
    conn, cursor = mock_connection
    cursor.__iter__.return_value = [
        ("ID", "NUMBER", 10),
        ("NAME", "VARCHAR2", 50),
        ("EMAIL", "VARCHAR2", 100)
    ]

    response = client.get("/tables/employees/columns")

    assert response.status_code == 200
    assert "columns" in response.json()
    expected_columns = [
        {"name": "ID", "type": "NUMBER", "length": 10},
        {"name": "NAME", "type": "VARCHAR2", "length": 50},
        {"name": "EMAIL", "type": "VARCHAR2", "length": 100}
    ]
    assert response.json()["columns"] == expected_columns

def test_query_table_success(mock_connection):
    conn, cursor = mock_connection
    # 模拟validate_table返回True
    with patch("mcp_oracle_server.validate_table", return_value=True):
        cursor.description = [("ID",), ("NAME",), ("EMAIL",)]
        cursor.__iter__.return_value = [
            (1, "John Doe", "<EMAIL>"),
            (2, "Jane Smith", "<EMAIL>")
        ]

        response = client.post("/tables/employees/query?limit=10")

        assert response.status_code == 200
        assert "data" in response.json()
        expected_data = [
            {"ID": 1, "NAME": "John Doe", "EMAIL": "<EMAIL>"},
            {"ID": 2, "NAME": "Jane Smith", "EMAIL": "<EMAIL>"}
        ]
        assert response.json()["data"] == expected_data

def test_query_table_not_found(mock_connection):
    conn, cursor = mock_connection
    # 模拟validate_table返回False
    with patch("mcp_oracle_server.validate_table", return_value=False):
        response = client.post("/tables/nonexistent/query")

        assert response.status_code == 404
        assert "Table does not exist" in response.json()["detail"]

def test_insert_data_success(mock_connection):
    conn, cursor = mock_connection
    cursor.rowcount = 1

    # 模拟validate_table返回True
    with patch("mcp_oracle_server.validate_table", return_value=True):
        test_data = {"name": "Test User", "email": "<EMAIL>"}
        response = client.post("/tables/employees/insert", json=test_data)

        assert response.status_code == 200
        assert response.json()["status"] == "success"
        assert response.json()["row_count"] == 1

def test_insert_data_table_not_found(mock_connection):
    conn, cursor = mock_connection

    # 模拟validate_table返回False
    with patch("mcp_oracle_server.validate_table", return_value=False):
        test_data = {"name": "Test User", "email": "<EMAIL>"}
        response = client.post("/tables/nonexistent/insert", json=test_data)

        assert response.status_code == 404
        assert "Table does not exist" in response.json()["detail"]

def test_execute_ddl_success(mock_connection):
    conn, cursor = mock_connection

    sql = "CREATE TABLE test_table (id NUMBER PRIMARY KEY, name VARCHAR2(50))"
    response = client.post("/ddl", json={"sql": sql})

    assert response.status_code == 200
    assert response.json()["status"] == "success"
    assert response.json()["sql_executed"] == sql

def test_execute_ddl_forbidden_operation():
    # 测试禁止的操作 - 使用CREATE语句但包含DROP关键字
    sql = "CREATE TABLE test_table AS SELECT * FROM old_table; DROP TABLE old_table"
    response = client.post("/ddl", json={"sql": sql})

    assert response.status_code == 403
    assert "Forbidden SQL operation detected" in response.json()["detail"]

def test_execute_ddl_invalid_operation():
    # 测试不允许的操作类型
    sql = "SELECT * FROM test_table"
    response = client.post("/ddl", json={"sql": sql})

    assert response.status_code == 403
    assert "Only DDL operations allowed" in response.json()["detail"]